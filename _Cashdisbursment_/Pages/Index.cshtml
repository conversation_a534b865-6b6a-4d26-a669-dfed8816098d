﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Home";
}

<div class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="display-5 mb-4">Professional Cash Disbursement Management</h1>
                    <p class="lead mb-4">Streamline fund applications, approvals, and disbursements with our comprehensive management platform designed for efficiency and transparency.</p>
                    <div class="d-flex flex-column flex-sm-row gap-3">
                        @if (Model.IsAuthenticated)
                        {
                            @if (Model.IsAdmin)
                            {
                                <a asp-page="/Admin/Dashboard" class="btn btn-light btn-lg">
                                    Access Admin Portal
                                </a>
                            }
                            else
                            {
                                <a asp-page="/Company/Dashboard" class="btn btn-light btn-lg">
                                    Go to Dashboard
                                </a>
                            }
                        }
                        else
                        {
                            <a asp-page="/Account/Login" class="btn btn-light btn-lg">
                                Sign In
                            </a>
                            <a asp-page="/Account/Register" class="btn btn-outline-light btn-lg">
                                Register Company
                            </a>
                        }
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-visual text-center">
                    <div class="hero-icon-container">
                        <i class="fas fa-university"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<section class="py-section">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-section">
                <h2 class="h3 mb-3">How It Works</h2>
                <p class="text-muted">Simple and efficient fund management process</p>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-4">
                <div class="process-step text-center">
                    <div class="step-icon mb-3">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h5 class="mb-3">Register</h5>
                    <p class="text-muted">Companies register and get verified by administrators through our secure onboarding process.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="process-step text-center">
                    <div class="step-icon mb-3">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h5 class="mb-3">Apply</h5>
                    <p class="text-muted">Submit comprehensive fund applications with detailed documentation and supporting materials.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="process-step text-center">
                    <div class="step-icon mb-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h5 class="mb-3">Receive</h5>
                    <p class="text-muted">Get funds disbursed efficiently after completing the multi-level approval process.</p>
                </div>
            </div>
        </div>
    </div>
</section>

    <div class="row mt-5">
        <div class="col-12 text-center mb-5">
            <h2 class="h3 mb-3">Key Features</h2>
            <p class="text-muted">Everything you need for efficient fund management</p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle fa-2x text-success me-3"></i>
                </div>
                <div>
                    <h5>Multi-level Approval</h5>
                    <p class="text-muted">Configurable approval workflow with multiple levels of authorization</p>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-envelope fa-2x text-primary me-3"></i>
                </div>
                <div>
                    <h5>Email Notifications</h5>
                    <p class="text-muted">Automated email updates for all application status changes</p>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-file-pdf fa-2x text-danger me-3"></i>
                </div>
                <div>
                    <h5>Document Management</h5>
                    <p class="text-muted">Upload and manage supporting documents for applications</p>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-chart-bar fa-2x text-info me-3"></i>
                </div>
                <div>
                    <h5>Real-time Tracking</h5>
                    <p class="text-muted">Track application status and disbursement progress in real-time</p>
                </div>
            </div>
        </div>
    </div>
</div>
